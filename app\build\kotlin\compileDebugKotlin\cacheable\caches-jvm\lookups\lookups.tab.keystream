  Manifest android  CAMERA android.Manifest.permission  simple_spinner_dropdown_item android.R.layout  simple_spinner_item android.R.layout  SuppressLint android.annotation  Activity android.app  Application android.app  ActivityMainBinding android.app.Activity  Boolean android.app.Activity  Build android.app.Activity  ExperimentalGetImage android.app.Activity  Int android.app.Activity  Log android.app.Activity  RequestPermission android.app.Activity  ScanResultsHolder android.app.Activity  SeekBar android.app.Activity  String android.app.Activity  Toast android.app.Activity  View android.app.Activity  clearResults android.app.Activity  format android.app.Activity  getValue android.app.Activity  
isNotEmpty android.app.Activity  joinToString android.app.Activity  layoutInflater android.app.Activity  let android.app.Activity  onCreate android.app.Activity  provideDelegate android.app.Activity  takeLast android.app.Activity  	viewModel android.app.Activity  
viewModels android.app.Activity  
windowManager android.app.Activity  OnSeekBarChangeListener android.app.Activity.SeekBar  
sendBroadcast android.app.Application  Context android.content  Intent android.content  ActivityMainBinding android.content.Context  Boolean android.content.Context  Build android.content.Context  ExperimentalGetImage android.content.Context  Int android.content.Context  Log android.content.Context  RequestPermission android.content.Context  ScanResultsHolder android.content.Context  SeekBar android.content.Context  String android.content.Context  Toast android.content.Context  View android.content.Context  clearResults android.content.Context  format android.content.Context  getValue android.content.Context  
isNotEmpty android.content.Context  joinToString android.content.Context  let android.content.Context  provideDelegate android.content.Context  takeLast android.content.Context  	viewModel android.content.Context  
viewModels android.content.Context  OnSeekBarChangeListener android.content.Context.SeekBar  ActivityMainBinding android.content.ContextWrapper  Boolean android.content.ContextWrapper  Build android.content.ContextWrapper  ExperimentalGetImage android.content.ContextWrapper  Int android.content.ContextWrapper  Log android.content.ContextWrapper  RequestPermission android.content.ContextWrapper  ScanResultsHolder android.content.ContextWrapper  SeekBar android.content.ContextWrapper  String android.content.ContextWrapper  Toast android.content.ContextWrapper  View android.content.ContextWrapper  clearResults android.content.ContextWrapper  format android.content.ContextWrapper  getValue android.content.ContextWrapper  
isNotEmpty android.content.ContextWrapper  joinToString android.content.ContextWrapper  let android.content.ContextWrapper  provideDelegate android.content.ContextWrapper  
sendBroadcast android.content.ContextWrapper  takeLast android.content.ContextWrapper  	viewModel android.content.ContextWrapper  
viewModels android.content.ContextWrapper  OnSeekBarChangeListener &android.content.ContextWrapper.SeekBar  BroadCastUtils android.content.Intent  apply android.content.Intent  putExtra android.content.Intent  PackageManager android.content.pm  PERMISSION_GRANTED !android.content.pm.PackageManager  AttributeSet android.graphics  Barcode android.graphics  Bitmap android.graphics  Canvas android.graphics  Color android.graphics  Context android.graphics  DetectedQRInfo android.graphics  Float android.graphics  ImageFormat android.graphics  Int android.graphics  JvmOverloads android.graphics  List android.graphics  Log android.graphics  Paint android.graphics  Point android.graphics  Rect android.graphics  RectF android.graphics  String android.graphics  Typeface android.graphics  View android.graphics  apply android.graphics  forEach android.graphics  let android.graphics  minOf android.graphics  
mutableListOf android.graphics  height android.graphics.Bitmap  scale android.graphics.Bitmap  width android.graphics.Bitmap  drawLine android.graphics.Canvas  drawRect android.graphics.Canvas  
drawRoundRect android.graphics.Canvas  drawText android.graphics.Canvas  BLACK android.graphics.Color  GREEN android.graphics.Color  Color android.graphics.Paint  Paint android.graphics.Paint  Style android.graphics.Paint  Typeface android.graphics.Paint  alpha android.graphics.Paint  apply android.graphics.Paint  color android.graphics.Paint  
getTextBounds android.graphics.Paint  isAntiAlias android.graphics.Paint  strokeWidth android.graphics.Paint  style android.graphics.Paint  textSize android.graphics.Paint  typeface android.graphics.Paint  STROKE android.graphics.Paint.Style  x android.graphics.Point  y android.graphics.Point  bottom android.graphics.Rect  height android.graphics.Rect  left android.graphics.Rect  let android.graphics.Rect  right android.graphics.Rect  top android.graphics.Rect  width android.graphics.Rect  bottom android.graphics.RectF  height android.graphics.RectF  left android.graphics.RectF  right android.graphics.RectF  top android.graphics.RectF  width android.graphics.RectF  DEFAULT_BOLD android.graphics.Typeface  CameraCharacteristics android.hardware.camera2  Image 
android.media  height android.media.Image  width android.media.Image  Build 
android.os  Bundle 
android.os  SDK_INT android.os.Build.VERSION  O android.os.Build.VERSION_CODES  R android.os.Build.VERSION_CODES  AttributeSet android.util  DisplayMetrics android.util  Log android.util  Range android.util  Rational android.util  Size android.util  heightPixels android.util.DisplayMetrics  widthPixels android.util.DisplayMetrics  d android.util.Log  e android.util.Log  w android.util.Log  lower android.util.Range  upper android.util.Range  toFloat android.util.Rational  height android.util.Size  width android.util.Size  LayoutInflater android.view  MotionEvent android.view  View android.view  
WindowMetrics android.view  ActivityMainBinding  android.view.ContextThemeWrapper  Boolean  android.view.ContextThemeWrapper  Build  android.view.ContextThemeWrapper  ExperimentalGetImage  android.view.ContextThemeWrapper  Int  android.view.ContextThemeWrapper  Log  android.view.ContextThemeWrapper  RequestPermission  android.view.ContextThemeWrapper  ScanResultsHolder  android.view.ContextThemeWrapper  SeekBar  android.view.ContextThemeWrapper  String  android.view.ContextThemeWrapper  Toast  android.view.ContextThemeWrapper  View  android.view.ContextThemeWrapper  clearResults  android.view.ContextThemeWrapper  format  android.view.ContextThemeWrapper  getValue  android.view.ContextThemeWrapper  
isNotEmpty  android.view.ContextThemeWrapper  joinToString  android.view.ContextThemeWrapper  let  android.view.ContextThemeWrapper  provideDelegate  android.view.ContextThemeWrapper  takeLast  android.view.ContextThemeWrapper  	viewModel  android.view.ContextThemeWrapper  
viewModels  android.view.ContextThemeWrapper  OnSeekBarChangeListener (android.view.ContextThemeWrapper.SeekBar  
getMetrics android.view.Display  from android.view.LayoutInflater  ArrayAdapter android.view.View  Color android.view.View  ControlPanelOverlayBinding android.view.View  DetectedQRInfo android.view.View  	Exception android.view.View  
FrequencyUnit android.view.View  GONE android.view.View  LayoutInflater android.view.View  Log android.view.View  OnClickListener android.view.View  Paint android.view.View  Rect android.view.View  RectF android.view.View  ScanSettings android.view.View  ScanSettingsManager android.view.View  Typeface android.view.View  VISIBLE android.view.View  View android.view.View  android android.view.View  apply android.view.View  
coerceAtLeast android.view.View  context android.view.View  getCurrentSettings android.view.View  height android.view.View  
invalidate android.view.View  	isEnabled android.view.View  let android.view.View  map android.view.View  minOf android.view.View  
mutableListOf android.view.View  onDraw android.view.View  setOnClickListener android.view.View  toIntOrNull android.view.View  updateSettings android.view.View  
visibility android.view.View  width android.view.View  <SAM-CONSTRUCTOR> !android.view.View.OnClickListener  ArrayAdapter android.view.ViewGroup  ControlPanelOverlayBinding android.view.ViewGroup  	Exception android.view.ViewGroup  
FrequencyUnit android.view.ViewGroup  LayoutInflater android.view.ViewGroup  ScanSettings android.view.ViewGroup  ScanSettingsManager android.view.ViewGroup  View android.view.ViewGroup  android android.view.ViewGroup  
coerceAtLeast android.view.ViewGroup  getCurrentSettings android.view.ViewGroup  map android.view.ViewGroup  toIntOrNull android.view.ViewGroup  updateSettings android.view.ViewGroup  currentWindowMetrics android.view.WindowManager  defaultDisplay android.view.WindowManager  bounds android.view.WindowMetrics  ArrayAdapter android.widget  Button android.widget  EditText android.widget  FrameLayout android.widget  LinearLayout android.widget  SeekBar android.widget  Spinner android.widget  TextView android.widget  Toast android.widget  adapter android.widget.AbsSpinner  setSelection android.widget.AbsSpinner  selectedItemPosition android.widget.AdapterView  setDropDownViewResource android.widget.ArrayAdapter  	isEnabled android.widget.Button  setOnClickListener android.widget.Button  text android.widget.Button  
visibility android.widget.Button  setText android.widget.EditText  text android.widget.EditText  ArrayAdapter android.widget.FrameLayout  ControlPanelOverlayBinding android.widget.FrameLayout  	Exception android.widget.FrameLayout  
FrequencyUnit android.widget.FrameLayout  LayoutInflater android.widget.FrameLayout  ScanSettings android.widget.FrameLayout  ScanSettingsManager android.widget.FrameLayout  View android.widget.FrameLayout  android android.widget.FrameLayout  
coerceAtLeast android.widget.FrameLayout  getCurrentSettings android.widget.FrameLayout  map android.widget.FrameLayout  toIntOrNull android.widget.FrameLayout  updateSettings android.widget.FrameLayout  
visibility android.widget.LinearLayout  max android.widget.ProgressBar  progress android.widget.ProgressBar  OnSeekBarChangeListener android.widget.SeekBar  progress android.widget.SeekBar  setOnSeekBarChangeListener android.widget.SeekBar  adapter android.widget.Spinner  selectedItemPosition android.widget.Spinner  setSelection android.widget.Spinner  setText android.widget.TextView  text android.widget.TextView  LENGTH_SHORT android.widget.Toast  makeText android.widget.Toast  show android.widget.Toast  ComponentActivity androidx.activity  
viewModels androidx.activity  ActivityMainBinding #androidx.activity.ComponentActivity  Boolean #androidx.activity.ComponentActivity  Build #androidx.activity.ComponentActivity  Bundle #androidx.activity.ComponentActivity  	Companion #androidx.activity.ComponentActivity  ExperimentalGetImage #androidx.activity.ComponentActivity  Float #androidx.activity.ComponentActivity  Int #androidx.activity.ComponentActivity  Log #androidx.activity.ComponentActivity  OptIn #androidx.activity.ComponentActivity  QrScanViewModel #androidx.activity.ComponentActivity  RequestPermission #androidx.activity.ComponentActivity  ScanResultsHolder #androidx.activity.ComponentActivity  ScanSettings #androidx.activity.ComponentActivity  SeekBar #androidx.activity.ComponentActivity  String #androidx.activity.ComponentActivity  Toast #androidx.activity.ComponentActivity  View #androidx.activity.ComponentActivity  clearResults #androidx.activity.ComponentActivity  format #androidx.activity.ComponentActivity  getValue #androidx.activity.ComponentActivity  
isNotEmpty #androidx.activity.ComponentActivity  joinToString #androidx.activity.ComponentActivity  let #androidx.activity.ComponentActivity  provideDelegate #androidx.activity.ComponentActivity  registerForActivityResult #androidx.activity.ComponentActivity  takeLast #androidx.activity.ComponentActivity  	viewModel #androidx.activity.ComponentActivity  
viewModels #androidx.activity.ComponentActivity  ActivityMainBinding -androidx.activity.ComponentActivity.Companion  Build -androidx.activity.ComponentActivity.Companion  ExperimentalGetImage -androidx.activity.ComponentActivity.Companion  Log -androidx.activity.ComponentActivity.Companion  RequestPermission -androidx.activity.ComponentActivity.Companion  ScanResultsHolder -androidx.activity.ComponentActivity.Companion  String -androidx.activity.ComponentActivity.Companion  Toast -androidx.activity.ComponentActivity.Companion  View -androidx.activity.ComponentActivity.Companion  clearResults -androidx.activity.ComponentActivity.Companion  format -androidx.activity.ComponentActivity.Companion  getValue -androidx.activity.ComponentActivity.Companion  
isNotEmpty -androidx.activity.ComponentActivity.Companion  joinToString -androidx.activity.ComponentActivity.Companion  let -androidx.activity.ComponentActivity.Companion  provideDelegate -androidx.activity.ComponentActivity.Companion  takeLast -androidx.activity.ComponentActivity.Companion  	viewModel -androidx.activity.ComponentActivity.Companion  
viewModels -androidx.activity.ComponentActivity.Companion  OnSeekBarChangeListener +androidx.activity.ComponentActivity.SeekBar  ActivityResultCallback androidx.activity.result  ActivityResultLauncher androidx.activity.result  <SAM-CONSTRUCTOR> /androidx.activity.result.ActivityResultCallback  launch /androidx.activity.result.ActivityResultLauncher  ActivityResultContracts !androidx.activity.result.contract  RequestPermission 9androidx.activity.result.contract.ActivityResultContracts  OptIn androidx.annotation  RequiresApi androidx.annotation  AppCompatActivity androidx.appcompat.app  ActivityMainBinding (androidx.appcompat.app.AppCompatActivity  Boolean (androidx.appcompat.app.AppCompatActivity  Build (androidx.appcompat.app.AppCompatActivity  ExperimentalGetImage (androidx.appcompat.app.AppCompatActivity  Int (androidx.appcompat.app.AppCompatActivity  Log (androidx.appcompat.app.AppCompatActivity  RequestPermission (androidx.appcompat.app.AppCompatActivity  ScanResultsHolder (androidx.appcompat.app.AppCompatActivity  SeekBar (androidx.appcompat.app.AppCompatActivity  String (androidx.appcompat.app.AppCompatActivity  Toast (androidx.appcompat.app.AppCompatActivity  View (androidx.appcompat.app.AppCompatActivity  clearResults (androidx.appcompat.app.AppCompatActivity  format (androidx.appcompat.app.AppCompatActivity  getValue (androidx.appcompat.app.AppCompatActivity  
isNotEmpty (androidx.appcompat.app.AppCompatActivity  joinToString (androidx.appcompat.app.AppCompatActivity  let (androidx.appcompat.app.AppCompatActivity  onCreate (androidx.appcompat.app.AppCompatActivity  	onDestroy (androidx.appcompat.app.AppCompatActivity  provideDelegate (androidx.appcompat.app.AppCompatActivity  setContentView (androidx.appcompat.app.AppCompatActivity  takeLast (androidx.appcompat.app.AppCompatActivity  	viewModel (androidx.appcompat.app.AppCompatActivity  
viewModels (androidx.appcompat.app.AppCompatActivity  OnSeekBarChangeListener 0androidx.appcompat.app.AppCompatActivity.SeekBar  Camera2CameraInfo androidx.camera.camera2.interop  ExperimentalCamera2Interop androidx.camera.camera2.interop  Camera androidx.camera.core  
CameraControl androidx.camera.core  
CameraInfo androidx.camera.core  CameraSelector androidx.camera.core  ConcurrentCamera androidx.camera.core  ExperimentalGetImage androidx.camera.core  
ExposureState androidx.camera.core  
ImageAnalysis androidx.camera.core  	ImageInfo androidx.camera.core  
ImageProxy androidx.camera.core  Preview androidx.camera.core  ResolutionInfo androidx.camera.core  UseCase androidx.camera.core  	ZoomState androidx.camera.core  
cameraControl androidx.camera.core.Camera  
cameraInfo androidx.camera.core.Camera  let androidx.camera.core.Camera  enableTorch "androidx.camera.core.CameraControl  setExposureCompensationIndex "androidx.camera.core.CameraControl  setZoomRatio "androidx.camera.core.CameraControl  
exposureState androidx.camera.core.CameraInfo  hasFlashUnit androidx.camera.core.CameraInfo  	zoomState androidx.camera.core.CameraInfo  DEFAULT_BACK_CAMERA #androidx.camera.core.CameraSelector  exposureCompensationIndex "androidx.camera.core.ExposureState  exposureCompensationRange "androidx.camera.core.ExposureState  exposureCompensationStep "androidx.camera.core.ExposureState  Analyzer "androidx.camera.core.ImageAnalysis  Builder "androidx.camera.core.ImageAnalysis  STRATEGY_KEEP_ONLY_LATEST "androidx.camera.core.ImageAnalysis  also "androidx.camera.core.ImageAnalysis  let "androidx.camera.core.ImageAnalysis  resolutionInfo "androidx.camera.core.ImageAnalysis  setAnalyzer "androidx.camera.core.ImageAnalysis  build *androidx.camera.core.ImageAnalysis.Builder  setBackpressureStrategy *androidx.camera.core.ImageAnalysis.Builder  setResolutionSelector *androidx.camera.core.ImageAnalysis.Builder  rotationDegrees androidx.camera.core.ImageInfo  close androidx.camera.core.ImageProxy  image androidx.camera.core.ImageProxy  	imageInfo androidx.camera.core.ImageProxy  Builder androidx.camera.core.Preview  SurfaceProvider androidx.camera.core.Preview  also androidx.camera.core.Preview  surfaceProvider androidx.camera.core.Preview  build $androidx.camera.core.Preview.Builder  setResolutionSelector $androidx.camera.core.Preview.Builder  maxZoomRatio androidx.camera.core.ZoomState  minZoomRatio androidx.camera.core.ZoomState  	zoomRatio androidx.camera.core.ZoomState  ResolutionFilter 'androidx.camera.core.resolutionselector  ResolutionSelector 'androidx.camera.core.resolutionselector  ResolutionStrategy 'androidx.camera.core.resolutionselector  <SAM-CONSTRUCTOR> 8androidx.camera.core.resolutionselector.ResolutionFilter  Builder :androidx.camera.core.resolutionselector.ResolutionSelector  build Bandroidx.camera.core.resolutionselector.ResolutionSelector.Builder  setResolutionFilter Bandroidx.camera.core.resolutionselector.ResolutionSelector.Builder  setResolutionStrategy Bandroidx.camera.core.resolutionselector.ResolutionSelector.Builder  'FALLBACK_RULE_CLOSEST_LOWER_THEN_HIGHER :androidx.camera.core.resolutionselector.ResolutionStrategy  ProcessCameraProvider androidx.camera.lifecycle  	Companion /androidx.camera.lifecycle.ProcessCameraProvider  bindToLifecycle /androidx.camera.lifecycle.ProcessCameraProvider  getInstance /androidx.camera.lifecycle.ProcessCameraProvider  	unbindAll /androidx.camera.lifecycle.ProcessCameraProvider  getInstance 9androidx.camera.lifecycle.ProcessCameraProvider.Companion  PreviewView androidx.camera.view  surfaceProvider  androidx.camera.view.PreviewView  ConstraintLayout  androidx.constraintlayout.widget  ActivityMainBinding #androidx.core.app.ComponentActivity  Boolean #androidx.core.app.ComponentActivity  Build #androidx.core.app.ComponentActivity  Bundle #androidx.core.app.ComponentActivity  ExperimentalGetImage #androidx.core.app.ComponentActivity  Float #androidx.core.app.ComponentActivity  Int #androidx.core.app.ComponentActivity  Log #androidx.core.app.ComponentActivity  OptIn #androidx.core.app.ComponentActivity  QrScanViewModel #androidx.core.app.ComponentActivity  RequestPermission #androidx.core.app.ComponentActivity  ScanResultsHolder #androidx.core.app.ComponentActivity  ScanSettings #androidx.core.app.ComponentActivity  SeekBar #androidx.core.app.ComponentActivity  String #androidx.core.app.ComponentActivity  Toast #androidx.core.app.ComponentActivity  View #androidx.core.app.ComponentActivity  clearResults #androidx.core.app.ComponentActivity  format #androidx.core.app.ComponentActivity  getValue #androidx.core.app.ComponentActivity  
isNotEmpty #androidx.core.app.ComponentActivity  joinToString #androidx.core.app.ComponentActivity  let #androidx.core.app.ComponentActivity  provideDelegate #androidx.core.app.ComponentActivity  takeLast #androidx.core.app.ComponentActivity  	viewModel #androidx.core.app.ComponentActivity  
viewModels #androidx.core.app.ComponentActivity  OnSeekBarChangeListener +androidx.core.app.ComponentActivity.SeekBar  
ContextCompat androidx.core.content  checkSelfPermission #androidx.core.content.ContextCompat  getMainExecutor #androidx.core.content.ContextCompat  scale androidx.core.graphics  ActivityMainBinding &androidx.fragment.app.FragmentActivity  Boolean &androidx.fragment.app.FragmentActivity  Build &androidx.fragment.app.FragmentActivity  ExperimentalGetImage &androidx.fragment.app.FragmentActivity  Int &androidx.fragment.app.FragmentActivity  Log &androidx.fragment.app.FragmentActivity  RequestPermission &androidx.fragment.app.FragmentActivity  ScanResultsHolder &androidx.fragment.app.FragmentActivity  SeekBar &androidx.fragment.app.FragmentActivity  String &androidx.fragment.app.FragmentActivity  Toast &androidx.fragment.app.FragmentActivity  View &androidx.fragment.app.FragmentActivity  clearResults &androidx.fragment.app.FragmentActivity  format &androidx.fragment.app.FragmentActivity  getValue &androidx.fragment.app.FragmentActivity  
isNotEmpty &androidx.fragment.app.FragmentActivity  joinToString &androidx.fragment.app.FragmentActivity  let &androidx.fragment.app.FragmentActivity  onCreate &androidx.fragment.app.FragmentActivity  provideDelegate &androidx.fragment.app.FragmentActivity  takeLast &androidx.fragment.app.FragmentActivity  	viewModel &androidx.fragment.app.FragmentActivity  
viewModels &androidx.fragment.app.FragmentActivity  OnSeekBarChangeListener .androidx.fragment.app.FragmentActivity.SeekBar  AndroidViewModel androidx.lifecycle  LifecycleOwner androidx.lifecycle  LiveData androidx.lifecycle  MutableLiveData androidx.lifecycle  Observer androidx.lifecycle  viewModelScope androidx.lifecycle  getApplication #androidx.lifecycle.AndroidViewModel  	onCleared #androidx.lifecycle.AndroidViewModel  observe androidx.lifecycle.LiveData  value androidx.lifecycle.LiveData  observe "androidx.lifecycle.MutableLiveData  	postValue "androidx.lifecycle.MutableLiveData  value "androidx.lifecycle.MutableLiveData  <SAM-CONSTRUCTOR> androidx.lifecycle.Observer  	onCleared androidx.lifecycle.ViewModel  OnCompleteListener com.google.android.gms.tasks  OnFailureListener com.google.android.gms.tasks  OnSuccessListener com.google.android.gms.tasks  Task com.google.android.gms.tasks  <SAM-CONSTRUCTOR> /com.google.android.gms.tasks.OnCompleteListener  <SAM-CONSTRUCTOR> .com.google.android.gms.tasks.OnFailureListener  <SAM-CONSTRUCTOR> .com.google.android.gms.tasks.OnSuccessListener  addOnCompleteListener !com.google.android.gms.tasks.Task  addOnFailureListener !com.google.android.gms.tasks.Task  addOnSuccessListener !com.google.android.gms.tasks.Task  ListenableFuture !com.google.common.util.concurrent  addListener 2com.google.common.util.concurrent.ListenableFuture  get 2com.google.common.util.concurrent.ListenableFuture  BarcodeScanner com.google.mlkit.vision.barcode  BarcodeScannerOptions com.google.mlkit.vision.barcode  BarcodeScanning com.google.mlkit.vision.barcode  process .com.google.mlkit.vision.barcode.BarcodeScanner  Builder 5com.google.mlkit.vision.barcode.BarcodeScannerOptions  build =com.google.mlkit.vision.barcode.BarcodeScannerOptions.Builder  setBarcodeFormats =com.google.mlkit.vision.barcode.BarcodeScannerOptions.Builder  	getClient /com.google.mlkit.vision.barcode.BarcodeScanning  Barcode &com.google.mlkit.vision.barcode.common  FORMAT_QR_CODE .com.google.mlkit.vision.barcode.common.Barcode  boundingBox .com.google.mlkit.vision.barcode.common.Barcode  rawValue .com.google.mlkit.vision.barcode.common.Barcode  
InputImage com.google.mlkit.vision.common  fromMediaImage )com.google.mlkit.vision.common.InputImage  VerticalSeekBar )com.h6ah4i.android.widget.verticalseekbar  max 9com.h6ah4i.android.widget.verticalseekbar.VerticalSeekBar  progress 9com.h6ah4i.android.widget.verticalseekbar.VerticalSeekBar  setOnSeekBarChangeListener 9com.h6ah4i.android.widget.verticalseekbar.VerticalSeekBar  QrScanRepository com.yancao.qrscanner  R com.yancao.qrscanner  Barcode com.yancao.qrscanner.camera  BarcodeScannerOptions com.yancao.qrscanner.camera  BarcodeScanning com.yancao.qrscanner.camera  Boolean com.yancao.qrscanner.camera  
CameraControl com.yancao.qrscanner.camera  
CameraInfo com.yancao.qrscanner.camera  
CameraManager com.yancao.qrscanner.camera  CameraSelector com.yancao.qrscanner.camera  Context com.yancao.qrscanner.camera  
ContextCompat com.yancao.qrscanner.camera  	Exception com.yancao.qrscanner.camera  	Executors com.yancao.qrscanner.camera  ExperimentalCamera2Interop com.yancao.qrscanner.camera  ExperimentalGetImage com.yancao.qrscanner.camera  Float com.yancao.qrscanner.camera  
ImageAnalysis com.yancao.qrscanner.camera  
ImageProxy com.yancao.qrscanner.camera  
InputImage com.yancao.qrscanner.camera  Int com.yancao.qrscanner.camera  LifecycleOwner com.yancao.qrscanner.camera  List com.yancao.qrscanner.camera  Log com.yancao.qrscanner.camera  Long com.yancao.qrscanner.camera  OptIn com.yancao.qrscanner.camera  Preview com.yancao.qrscanner.camera  PreviewView com.yancao.qrscanner.camera  ProcessCameraProvider com.yancao.qrscanner.camera  RealtimeQRAnalyzer com.yancao.qrscanner.camera  ResolutionSelector com.yancao.qrscanner.camera  ResolutionStrategy com.yancao.qrscanner.camera  ScanResultsHolder com.yancao.qrscanner.camera  ScanSettingsManager com.yancao.qrscanner.camera  Size com.yancao.qrscanner.camera  String com.yancao.qrscanner.camera  System com.yancao.qrscanner.camera  Unit com.yancao.qrscanner.camera  UseCase com.yancao.qrscanner.camera  abs com.yancao.qrscanner.camera  addScanResults com.yancao.qrscanner.camera  also com.yancao.qrscanner.camera  coerceIn com.yancao.qrscanner.camera  	emptyList com.yancao.qrscanner.camera  getInstance com.yancao.qrscanner.camera  getScanIntervalMs com.yancao.qrscanner.camera  ifEmpty com.yancao.qrscanner.camera  
isNotEmpty com.yancao.qrscanner.camera  let com.yancao.qrscanner.camera  map com.yancao.qrscanner.camera  
mapNotNull com.yancao.qrscanner.camera  
mutableListOf com.yancao.qrscanner.camera  println com.yancao.qrscanner.camera  sortedBy com.yancao.qrscanner.camera  take com.yancao.qrscanner.camera  toTypedArray com.yancao.qrscanner.camera  CameraSelector )com.yancao.qrscanner.camera.CameraManager  
ContextCompat )com.yancao.qrscanner.camera.CameraManager  	Executors )com.yancao.qrscanner.camera.CameraManager  ExperimentalCamera2Interop )com.yancao.qrscanner.camera.CameraManager  
ImageAnalysis )com.yancao.qrscanner.camera.CameraManager  Log )com.yancao.qrscanner.camera.CameraManager  Preview )com.yancao.qrscanner.camera.CameraManager  ProcessCameraProvider )com.yancao.qrscanner.camera.CameraManager  RealtimeQRAnalyzer )com.yancao.qrscanner.camera.CameraManager  ResolutionSelector )com.yancao.qrscanner.camera.CameraManager  ResolutionStrategy )com.yancao.qrscanner.camera.CameraManager  Size )com.yancao.qrscanner.camera.CameraManager  abs )com.yancao.qrscanner.camera.CameraManager  also )com.yancao.qrscanner.camera.CameraManager  
cameraControl )com.yancao.qrscanner.camera.CameraManager  cameraExecutor )com.yancao.qrscanner.camera.CameraManager  
cameraInfo )com.yancao.qrscanner.camera.CameraManager  cameraProvider )com.yancao.qrscanner.camera.CameraManager  coerceIn )com.yancao.qrscanner.camera.CameraManager  context )com.yancao.qrscanner.camera.CameraManager  getCurrentExposureCompensation )com.yancao.qrscanner.camera.CameraManager  getCurrentZoomRatio )com.yancao.qrscanner.camera.CameraManager  getExposureCompensationStep )com.yancao.qrscanner.camera.CameraManager  getInstance )com.yancao.qrscanner.camera.CameraManager  getMaxExposureCompensation )com.yancao.qrscanner.camera.CameraManager  getMaxZoomRatio )com.yancao.qrscanner.camera.CameraManager  getMinExposureCompensation )com.yancao.qrscanner.camera.CameraManager  getMinZoomRatio )com.yancao.qrscanner.camera.CameraManager  
hasFlashlight )com.yancao.qrscanner.camera.CameraManager  ifEmpty )com.yancao.qrscanner.camera.CameraManager  
imageAnalyzer )com.yancao.qrscanner.camera.CameraManager  let )com.yancao.qrscanner.camera.CameraManager  map )com.yancao.qrscanner.camera.CameraManager  
mutableListOf )com.yancao.qrscanner.camera.CameraManager  
qrAnalyzer )com.yancao.qrscanner.camera.CameraManager  setExposureCompensation )com.yancao.qrscanner.camera.CameraManager  
setFlashlight )com.yancao.qrscanner.camera.CameraManager  setZoomRatio )com.yancao.qrscanner.camera.CameraManager  shutdown )com.yancao.qrscanner.camera.CameraManager  sortedBy )com.yancao.qrscanner.camera.CameraManager  startCamera )com.yancao.qrscanner.camera.CameraManager  take )com.yancao.qrscanner.camera.CameraManager  toTypedArray )com.yancao.qrscanner.camera.CameraManager  Analyzer )com.yancao.qrscanner.camera.ImageAnalysis  Barcode .com.yancao.qrscanner.camera.RealtimeQRAnalyzer  BarcodeScannerOptions .com.yancao.qrscanner.camera.RealtimeQRAnalyzer  BarcodeScanning .com.yancao.qrscanner.camera.RealtimeQRAnalyzer  
InputImage .com.yancao.qrscanner.camera.RealtimeQRAnalyzer  ScanResultsHolder .com.yancao.qrscanner.camera.RealtimeQRAnalyzer  ScanSettingsManager .com.yancao.qrscanner.camera.RealtimeQRAnalyzer  System .com.yancao.qrscanner.camera.RealtimeQRAnalyzer  addScanResults .com.yancao.qrscanner.camera.RealtimeQRAnalyzer  	emptyList .com.yancao.qrscanner.camera.RealtimeQRAnalyzer  getCurrentAnalyzeInterval .com.yancao.qrscanner.camera.RealtimeQRAnalyzer  getScanIntervalMs .com.yancao.qrscanner.camera.RealtimeQRAnalyzer  
isNotEmpty .com.yancao.qrscanner.camera.RealtimeQRAnalyzer  lastAnalyzedTimestamp .com.yancao.qrscanner.camera.RealtimeQRAnalyzer  
mapNotNull .com.yancao.qrscanner.camera.RealtimeQRAnalyzer  onQRCodeDetected .com.yancao.qrscanner.camera.RealtimeQRAnalyzer  onQRCodeWithPosition .com.yancao.qrscanner.camera.RealtimeQRAnalyzer  options .com.yancao.qrscanner.camera.RealtimeQRAnalyzer  println .com.yancao.qrscanner.camera.RealtimeQRAnalyzer  scanner .com.yancao.qrscanner.camera.RealtimeQRAnalyzer  ActivityMainBinding  com.yancao.qrscanner.databinding  ControlPanelOverlayBinding  com.yancao.qrscanner.databinding  btnControlPanel 4com.yancao.qrscanner.databinding.ActivityMainBinding  
btnFlashlight 4com.yancao.qrscanner.databinding.ActivityMainBinding  btnScan 4com.yancao.qrscanner.databinding.ActivityMainBinding  controlPanelView 4com.yancao.qrscanner.databinding.ActivityMainBinding  exposureControlPanel 4com.yancao.qrscanner.databinding.ActivityMainBinding  inflate 4com.yancao.qrscanner.databinding.ActivityMainBinding  	mySeekBar 4com.yancao.qrscanner.databinding.ActivityMainBinding  previewView 4com.yancao.qrscanner.databinding.ActivityMainBinding  
qrOverlayView 4com.yancao.qrscanner.databinding.ActivityMainBinding  root 4com.yancao.qrscanner.databinding.ActivityMainBinding  seekBarZoom 4com.yancao.qrscanner.databinding.ActivityMainBinding  tvExposureValue 4com.yancao.qrscanner.databinding.ActivityMainBinding  
tvScanResults 4com.yancao.qrscanner.databinding.ActivityMainBinding  tvZoomRatio 4com.yancao.qrscanner.databinding.ActivityMainBinding  zoomSliderContainer 4com.yancao.qrscanner.databinding.ActivityMainBinding  btnApplySettings ;com.yancao.qrscanner.databinding.ControlPanelOverlayBinding  btnCancelSettings ;com.yancao.qrscanner.databinding.ControlPanelOverlayBinding  etDrawFrequency ;com.yancao.qrscanner.databinding.ControlPanelOverlayBinding  etScanFrequency ;com.yancao.qrscanner.databinding.ControlPanelOverlayBinding  inflate ;com.yancao.qrscanner.databinding.ControlPanelOverlayBinding  spinnerDrawUnit ;com.yancao.qrscanner.databinding.ControlPanelOverlayBinding  spinnerScanUnit ;com.yancao.qrscanner.databinding.ControlPanelOverlayBinding  Boolean com.yancao.qrscanner.domain  
FrequencyUnit com.yancao.qrscanner.domain  Int com.yancao.qrscanner.domain  List com.yancao.qrscanner.domain  Long com.yancao.qrscanner.domain  MutableList com.yancao.qrscanner.domain  ScanResultsHolder com.yancao.qrscanner.domain  ScanSettings com.yancao.qrscanner.domain  ScanSettingsManager com.yancao.qrscanner.domain  String com.yancao.qrscanner.domain  Synchronized com.yancao.qrscanner.domain  Volatile com.yancao.qrscanner.domain  
coerceAtLeast com.yancao.qrscanner.domain  forEach com.yancao.qrscanner.domain  
isNotBlank com.yancao.qrscanner.domain  
isNotEmpty com.yancao.qrscanner.domain  
lastOrNull com.yancao.qrscanner.domain  
mutableListOf com.yancao.qrscanner.domain  println com.yancao.qrscanner.domain  toList com.yancao.qrscanner.domain  FRAMES )com.yancao.qrscanner.domain.FrequencyUnit  MILLISECONDS )com.yancao.qrscanner.domain.FrequencyUnit  displayName )com.yancao.qrscanner.domain.FrequencyUnit  ordinal )com.yancao.qrscanner.domain.FrequencyUnit  values )com.yancao.qrscanner.domain.FrequencyUnit  _scanResults -com.yancao.qrscanner.domain.ScanResultsHolder  addScanResults -com.yancao.qrscanner.domain.ScanResultsHolder  clearResults -com.yancao.qrscanner.domain.ScanResultsHolder  
isNotBlank -com.yancao.qrscanner.domain.ScanResultsHolder  
isNotEmpty -com.yancao.qrscanner.domain.ScanResultsHolder  
lastOrNull -com.yancao.qrscanner.domain.ScanResultsHolder  
mutableListOf -com.yancao.qrscanner.domain.ScanResultsHolder  println -com.yancao.qrscanner.domain.ScanResultsHolder  scanResults -com.yancao.qrscanner.domain.ScanResultsHolder  toList -com.yancao.qrscanner.domain.ScanResultsHolder  drawFrequencyUnit (com.yancao.qrscanner.domain.ScanSettings  drawFrequencyValue (com.yancao.qrscanner.domain.ScanSettings  scanFrequencyUnit (com.yancao.qrscanner.domain.ScanSettings  scanFrequencyValue (com.yancao.qrscanner.domain.ScanSettings  
FrequencyUnit /com.yancao.qrscanner.domain.ScanSettingsManager  ScanSettings /com.yancao.qrscanner.domain.ScanSettingsManager  
coerceAtLeast /com.yancao.qrscanner.domain.ScanSettingsManager  currentSettings /com.yancao.qrscanner.domain.ScanSettingsManager  getCurrentSettings /com.yancao.qrscanner.domain.ScanSettingsManager  getScanIntervalMs /com.yancao.qrscanner.domain.ScanSettingsManager  updateSettings /com.yancao.qrscanner.domain.ScanSettingsManager  ActivityMainBinding com.yancao.qrscanner.ui  AppCompatActivity com.yancao.qrscanner.ui  ArrayAdapter com.yancao.qrscanner.ui  AttributeSet com.yancao.qrscanner.ui  Barcode com.yancao.qrscanner.ui  Boolean com.yancao.qrscanner.ui  Build com.yancao.qrscanner.ui  Bundle com.yancao.qrscanner.ui  Canvas com.yancao.qrscanner.ui  Color com.yancao.qrscanner.ui  Context com.yancao.qrscanner.ui  ControlPanelOverlayBinding com.yancao.qrscanner.ui  ControlPanelView com.yancao.qrscanner.ui  DetectedQRInfo com.yancao.qrscanner.ui  	Exception com.yancao.qrscanner.ui  ExperimentalGetImage com.yancao.qrscanner.ui  Float com.yancao.qrscanner.ui  FrameLayout com.yancao.qrscanner.ui  
FrequencyUnit com.yancao.qrscanner.ui  Int com.yancao.qrscanner.ui  JvmOverloads com.yancao.qrscanner.ui  LayoutInflater com.yancao.qrscanner.ui  List com.yancao.qrscanner.ui  Log com.yancao.qrscanner.ui  OptIn com.yancao.qrscanner.ui  Paint com.yancao.qrscanner.ui  QrCodeOverlayView com.yancao.qrscanner.ui  QrScanActivity com.yancao.qrscanner.ui  QrScanViewModel com.yancao.qrscanner.ui  Rect com.yancao.qrscanner.ui  RectF com.yancao.qrscanner.ui  RequestPermission com.yancao.qrscanner.ui  ScanResultsHolder com.yancao.qrscanner.ui  ScanSettings com.yancao.qrscanner.ui  ScanSettingsManager com.yancao.qrscanner.ui  SeekBar com.yancao.qrscanner.ui  String com.yancao.qrscanner.ui  Toast com.yancao.qrscanner.ui  Typeface com.yancao.qrscanner.ui  Unit com.yancao.qrscanner.ui  View com.yancao.qrscanner.ui  android com.yancao.qrscanner.ui  apply com.yancao.qrscanner.ui  clearResults com.yancao.qrscanner.ui  
coerceAtLeast com.yancao.qrscanner.ui  forEach com.yancao.qrscanner.ui  format com.yancao.qrscanner.ui  getCurrentSettings com.yancao.qrscanner.ui  getValue com.yancao.qrscanner.ui  
isNotEmpty com.yancao.qrscanner.ui  joinToString com.yancao.qrscanner.ui  let com.yancao.qrscanner.ui  map com.yancao.qrscanner.ui  minOf com.yancao.qrscanner.ui  
mutableListOf com.yancao.qrscanner.ui  provideDelegate com.yancao.qrscanner.ui  takeLast com.yancao.qrscanner.ui  toIntOrNull com.yancao.qrscanner.ui  updateSettings com.yancao.qrscanner.ui  	viewModel com.yancao.qrscanner.ui  ArrayAdapter (com.yancao.qrscanner.ui.ControlPanelView  ControlPanelOverlayBinding (com.yancao.qrscanner.ui.ControlPanelView  
FrequencyUnit (com.yancao.qrscanner.ui.ControlPanelView  LayoutInflater (com.yancao.qrscanner.ui.ControlPanelView  ScanSettings (com.yancao.qrscanner.ui.ControlPanelView  ScanSettingsManager (com.yancao.qrscanner.ui.ControlPanelView  View (com.yancao.qrscanner.ui.ControlPanelView  android (com.yancao.qrscanner.ui.ControlPanelView  
applySettings (com.yancao.qrscanner.ui.ControlPanelView  binding (com.yancao.qrscanner.ui.ControlPanelView  
coerceAtLeast (com.yancao.qrscanner.ui.ControlPanelView  context (com.yancao.qrscanner.ui.ControlPanelView  getCurrentSettings (com.yancao.qrscanner.ui.ControlPanelView  hide (com.yancao.qrscanner.ui.ControlPanelView  loadCurrentSettings (com.yancao.qrscanner.ui.ControlPanelView  map (com.yancao.qrscanner.ui.ControlPanelView  
onPanelClosed (com.yancao.qrscanner.ui.ControlPanelView  onSettingsApplied (com.yancao.qrscanner.ui.ControlPanelView  setCallbacks (com.yancao.qrscanner.ui.ControlPanelView  setupClickListeners (com.yancao.qrscanner.ui.ControlPanelView  
setupSpinners (com.yancao.qrscanner.ui.ControlPanelView  show (com.yancao.qrscanner.ui.ControlPanelView  toIntOrNull (com.yancao.qrscanner.ui.ControlPanelView  updateSettings (com.yancao.qrscanner.ui.ControlPanelView  
visibility (com.yancao.qrscanner.ui.ControlPanelView  AttributeSet )com.yancao.qrscanner.ui.QrCodeOverlayView  Barcode )com.yancao.qrscanner.ui.QrCodeOverlayView  Canvas )com.yancao.qrscanner.ui.QrCodeOverlayView  Color )com.yancao.qrscanner.ui.QrCodeOverlayView  Context )com.yancao.qrscanner.ui.QrCodeOverlayView  DetectedQRInfo )com.yancao.qrscanner.ui.QrCodeOverlayView  Float )com.yancao.qrscanner.ui.QrCodeOverlayView  Int )com.yancao.qrscanner.ui.QrCodeOverlayView  JvmOverloads )com.yancao.qrscanner.ui.QrCodeOverlayView  List )com.yancao.qrscanner.ui.QrCodeOverlayView  Log )com.yancao.qrscanner.ui.QrCodeOverlayView  Paint )com.yancao.qrscanner.ui.QrCodeOverlayView  Rect )com.yancao.qrscanner.ui.QrCodeOverlayView  RectF )com.yancao.qrscanner.ui.QrCodeOverlayView  String )com.yancao.qrscanner.ui.QrCodeOverlayView  Typeface )com.yancao.qrscanner.ui.QrCodeOverlayView  apply )com.yancao.qrscanner.ui.QrCodeOverlayView  clearDetectedQRCodes )com.yancao.qrscanner.ui.QrCodeOverlayView  detectedQRCodes )com.yancao.qrscanner.ui.QrCodeOverlayView  
drawFrequency )com.yancao.qrscanner.ui.QrCodeOverlayView  drawQRCodeFromBarcode )com.yancao.qrscanner.ui.QrCodeOverlayView  drawQRFrame )com.yancao.qrscanner.ui.QrCodeOverlayView  drawTextNearPoint )com.yancao.qrscanner.ui.QrCodeOverlayView  frameCounter )com.yancao.qrscanner.ui.QrCodeOverlayView  height )com.yancao.qrscanner.ui.QrCodeOverlayView  
invalidate )com.yancao.qrscanner.ui.QrCodeOverlayView  let )com.yancao.qrscanner.ui.QrCodeOverlayView  minOf )com.yancao.qrscanner.ui.QrCodeOverlayView  
mutableListOf )com.yancao.qrscanner.ui.QrCodeOverlayView  scannedQRPaint )com.yancao.qrscanner.ui.QrCodeOverlayView  	textPaint )com.yancao.qrscanner.ui.QrCodeOverlayView  updateDetectedQRCodes )com.yancao.qrscanner.ui.QrCodeOverlayView  width )com.yancao.qrscanner.ui.QrCodeOverlayView  barcode 8com.yancao.qrscanner.ui.QrCodeOverlayView.DetectedQRInfo  offsetX 8com.yancao.qrscanner.ui.QrCodeOverlayView.DetectedQRInfo  offsetY 8com.yancao.qrscanner.ui.QrCodeOverlayView.DetectedQRInfo  scaleFactor 8com.yancao.qrscanner.ui.QrCodeOverlayView.DetectedQRInfo  ActivityMainBinding &com.yancao.qrscanner.ui.QrScanActivity  Build &com.yancao.qrscanner.ui.QrScanActivity  ExperimentalGetImage &com.yancao.qrscanner.ui.QrScanActivity  Log &com.yancao.qrscanner.ui.QrScanActivity  RequestPermission &com.yancao.qrscanner.ui.QrScanActivity  ScanResultsHolder &com.yancao.qrscanner.ui.QrScanActivity  String &com.yancao.qrscanner.ui.QrScanActivity  Toast &com.yancao.qrscanner.ui.QrScanActivity  View &com.yancao.qrscanner.ui.QrScanActivity  applyNewSettings &com.yancao.qrscanner.ui.QrScanActivity  binding &com.yancao.qrscanner.ui.QrScanActivity  clearResults &com.yancao.qrscanner.ui.QrScanActivity  format &com.yancao.qrscanner.ui.QrScanActivity  getValue &com.yancao.qrscanner.ui.QrScanActivity  handleFlashlightButtonClick &com.yancao.qrscanner.ui.QrScanActivity  handleScanButtonClick &com.yancao.qrscanner.ui.QrScanActivity  hideControlPanel &com.yancao.qrscanner.ui.QrScanActivity  isControlPanelVisible &com.yancao.qrscanner.ui.QrScanActivity  
isNotEmpty &com.yancao.qrscanner.ui.QrScanActivity  joinToString &com.yancao.qrscanner.ui.QrScanActivity  layoutInflater &com.yancao.qrscanner.ui.QrScanActivity  let &com.yancao.qrscanner.ui.QrScanActivity  permissionHelper &com.yancao.qrscanner.ui.QrScanActivity  provideDelegate &com.yancao.qrscanner.ui.QrScanActivity  setContentView &com.yancao.qrscanner.ui.QrScanActivity  setupClickListeners &com.yancao.qrscanner.ui.QrScanActivity  setupExposureControl &com.yancao.qrscanner.ui.QrScanActivity  setupObservers &com.yancao.qrscanner.ui.QrScanActivity  setupZoomControl &com.yancao.qrscanner.ui.QrScanActivity  showControlPanel &com.yancao.qrscanner.ui.QrScanActivity  takeLast &com.yancao.qrscanner.ui.QrScanActivity  toggleControlPanel &com.yancao.qrscanner.ui.QrScanActivity  updateControlsVisibility &com.yancao.qrscanner.ui.QrScanActivity  updateFlashlightButtonText &com.yancao.qrscanner.ui.QrScanActivity  updateScanButtonText &com.yancao.qrscanner.ui.QrScanActivity  updateScanResultsDisplay &com.yancao.qrscanner.ui.QrScanActivity  updateZoomDisplay &com.yancao.qrscanner.ui.QrScanActivity  	viewModel &com.yancao.qrscanner.ui.QrScanActivity  
viewModels &com.yancao.qrscanner.ui.QrScanActivity  OnSeekBarChangeListener com.yancao.qrscanner.ui.SeekBar  Activity com.yancao.qrscanner.utils  ActivityResultContracts com.yancao.qrscanner.utils  Bitmap com.yancao.qrscanner.utils  BitmapSizeReduce com.yancao.qrscanner.utils  BroadCastUtils com.yancao.qrscanner.utils  Build com.yancao.qrscanner.utils  	ByteArray com.yancao.qrscanner.utils  CIO com.yancao.qrscanner.utils  CodeIdentify com.yancao.qrscanner.utils  ComponentActivity com.yancao.qrscanner.utils  ContentNegotiation com.yancao.qrscanner.utils  
ContextCompat com.yancao.qrscanner.utils  DisplayMetrics com.yancao.qrscanner.utils  DisplayUtils com.yancao.qrscanner.utils  	Exception com.yancao.qrscanner.utils  
HttpClient com.yancao.qrscanner.utils  HttpResponse com.yancao.qrscanner.utils  HttpStatusCode com.yancao.qrscanner.utils  ImageHolder com.yancao.qrscanner.utils  Int com.yancao.qrscanner.utils  Json com.yancao.qrscanner.utils  
JsonObject com.yancao.qrscanner.utils  Manifest com.yancao.qrscanner.utils  
MessageDigest com.yancao.qrscanner.utils  PackageManager com.yancao.qrscanner.utils  Point com.yancao.qrscanner.utils  RequestPermission com.yancao.qrscanner.utils  RequiresApi com.yancao.qrscanner.utils  String com.yancao.qrscanner.utils  Suppress com.yancao.qrscanner.utils  UUID com.yancao.qrscanner.utils  Unit com.yancao.qrscanner.utils  
bodyAsText com.yancao.qrscanner.utils  decodeFromString com.yancao.qrscanner.utils  format com.yancao.qrscanner.utils  get com.yancao.qrscanner.utils  	headersOf com.yancao.qrscanner.utils  invoke com.yancao.qrscanner.utils  java com.yancao.qrscanner.utils  joinToString com.yancao.qrscanner.utils  
jsonObject com.yancao.qrscanner.utils  listOf com.yancao.qrscanner.utils  mapOf com.yancao.qrscanner.utils  min com.yancao.qrscanner.utils  post com.yancao.qrscanner.utils  reversed com.yancao.qrscanner.utils  scale com.yancao.qrscanner.utils  setBody com.yancao.qrscanner.utils  to com.yancao.qrscanner.utils  toByteArray com.yancao.qrscanner.utils  toRegex com.yancao.qrscanner.utils  toString com.yancao.qrscanner.utils  min +com.yancao.qrscanner.utils.BitmapSizeReduce  scale +com.yancao.qrscanner.utils.BitmapSizeReduce  BROADCAST_ACTION )com.yancao.qrscanner.utils.BroadCastUtils  BROADCAST_RAW_DATA )com.yancao.qrscanner.utils.BroadCastUtils  Build 'com.yancao.qrscanner.utils.CodeIdentify  CIO 'com.yancao.qrscanner.utils.CodeIdentify  ContentNegotiation 'com.yancao.qrscanner.utils.CodeIdentify  
HttpClient 'com.yancao.qrscanner.utils.CodeIdentify  HttpStatusCode 'com.yancao.qrscanner.utils.CodeIdentify  Json 'com.yancao.qrscanner.utils.CodeIdentify  
MessageDigest 'com.yancao.qrscanner.utils.CodeIdentify  String 'com.yancao.qrscanner.utils.CodeIdentify  UUID 'com.yancao.qrscanner.utils.CodeIdentify  
bodyAsText 'com.yancao.qrscanner.utils.CodeIdentify  codeClassfy 'com.yancao.qrscanner.utils.CodeIdentify  decodeFromString 'com.yancao.qrscanner.utils.CodeIdentify  format 'com.yancao.qrscanner.utils.CodeIdentify  get 'com.yancao.qrscanner.utils.CodeIdentify  headers 'com.yancao.qrscanner.utils.CodeIdentify  	headersOf 'com.yancao.qrscanner.utils.CodeIdentify  invoke 'com.yancao.qrscanner.utils.CodeIdentify  java 'com.yancao.qrscanner.utils.CodeIdentify  	joinToHex 'com.yancao.qrscanner.utils.CodeIdentify  joinToString 'com.yancao.qrscanner.utils.CodeIdentify  json 'com.yancao.qrscanner.utils.CodeIdentify  
jsonObject 'com.yancao.qrscanner.utils.CodeIdentify  listOf 'com.yancao.qrscanner.utils.CodeIdentify  
loginPassword 'com.yancao.qrscanner.utils.CodeIdentify  
loginUsername 'com.yancao.qrscanner.utils.CodeIdentify  mapOf 'com.yancao.qrscanner.utils.CodeIdentify  md5 'com.yancao.qrscanner.utils.CodeIdentify  parseTokenFromJson 'com.yancao.qrscanner.utils.CodeIdentify  post 'com.yancao.qrscanner.utils.CodeIdentify  reversed 'com.yancao.qrscanner.utils.CodeIdentify  setBody 'com.yancao.qrscanner.utils.CodeIdentify  statusCallback 'com.yancao.qrscanner.utils.CodeIdentify  to 'com.yancao.qrscanner.utils.CodeIdentify  toByteArray 'com.yancao.qrscanner.utils.CodeIdentify  toRegex 'com.yancao.qrscanner.utils.CodeIdentify  toString 'com.yancao.qrscanner.utils.CodeIdentify  token 'com.yancao.qrscanner.utils.CodeIdentify  tokenGetter 'com.yancao.qrscanner.utils.CodeIdentify  Activity 'com.yancao.qrscanner.utils.DisplayUtils  Build 'com.yancao.qrscanner.utils.DisplayUtils  DisplayMetrics 'com.yancao.qrscanner.utils.DisplayUtils  Point 'com.yancao.qrscanner.utils.DisplayUtils  Suppress 'com.yancao.qrscanner.utils.DisplayUtils  Build 1com.yancao.qrscanner.utils.DisplayUtils.Companion  DisplayMetrics 1com.yancao.qrscanner.utils.DisplayUtils.Companion  Point 1com.yancao.qrscanner.utils.DisplayUtils.Companion  ActivityResultContracts ,com.yancao.qrscanner.utils.RequestPermission  
ContextCompat ,com.yancao.qrscanner.utils.RequestPermission  Manifest ,com.yancao.qrscanner.utils.RequestPermission  PackageManager ,com.yancao.qrscanner.utils.RequestPermission  activity ,com.yancao.qrscanner.utils.RequestPermission  checkAndRequestCameraPermission ,com.yancao.qrscanner.utils.RequestPermission  onPermissionDenied ,com.yancao.qrscanner.utils.RequestPermission  onPermissionGranted ,com.yancao.qrscanner.utils.RequestPermission  permissionLauncher ,com.yancao.qrscanner.utils.RequestPermission  AndroidViewModel com.yancao.qrscanner.viewModel  Application com.yancao.qrscanner.viewModel  Barcode com.yancao.qrscanner.viewModel  Boolean com.yancao.qrscanner.viewModel  BroadCastUtils com.yancao.qrscanner.viewModel  Build com.yancao.qrscanner.viewModel  
CameraManager com.yancao.qrscanner.viewModel  CodeIdentify com.yancao.qrscanner.viewModel  Dispatchers com.yancao.qrscanner.viewModel  	Exception com.yancao.qrscanner.viewModel  ExperimentalGetImage com.yancao.qrscanner.viewModel  Float com.yancao.qrscanner.viewModel  Int com.yancao.qrscanner.viewModel  Intent com.yancao.qrscanner.viewModel  LifecycleOwner com.yancao.qrscanner.viewModel  List com.yancao.qrscanner.viewModel  LiveData com.yancao.qrscanner.viewModel  MutableLiveData com.yancao.qrscanner.viewModel  OptIn com.yancao.qrscanner.viewModel  Pair com.yancao.qrscanner.viewModel  PreviewView com.yancao.qrscanner.viewModel  QrScanViewModel com.yancao.qrscanner.viewModel  RequiresApi com.yancao.qrscanner.viewModel  ScanResultsHolder com.yancao.qrscanner.viewModel  String com.yancao.qrscanner.viewModel  System com.yancao.qrscanner.viewModel  _codeIdentifyStatus com.yancao.qrscanner.viewModel  apply com.yancao.qrscanner.viewModel  codeIdentify com.yancao.qrscanner.viewModel  filter com.yancao.qrscanner.viewModel  forEach com.yancao.qrscanner.viewModel  
isNotEmpty com.yancao.qrscanner.viewModel  launch com.yancao.qrscanner.viewModel  mutableSetOf com.yancao.qrscanner.viewModel  
sendBroadcast com.yancao.qrscanner.viewModel  withContext com.yancao.qrscanner.viewModel  BroadCastUtils .com.yancao.qrscanner.viewModel.QrScanViewModel  Build .com.yancao.qrscanner.viewModel.QrScanViewModel  
CameraManager .com.yancao.qrscanner.viewModel.QrScanViewModel  CodeIdentify .com.yancao.qrscanner.viewModel.QrScanViewModel  DEBOUNCE_DELAY .com.yancao.qrscanner.viewModel.QrScanViewModel  Dispatchers .com.yancao.qrscanner.viewModel.QrScanViewModel  ExperimentalGetImage .com.yancao.qrscanner.viewModel.QrScanViewModel  Intent .com.yancao.qrscanner.viewModel.QrScanViewModel  MutableLiveData .com.yancao.qrscanner.viewModel.QrScanViewModel  Pair .com.yancao.qrscanner.viewModel.QrScanViewModel  ScanResultsHolder .com.yancao.qrscanner.viewModel.QrScanViewModel  System .com.yancao.qrscanner.viewModel.QrScanViewModel  _codeIdentifyStatus .com.yancao.qrscanner.viewModel.QrScanViewModel  _exposureCompensation .com.yancao.qrscanner.viewModel.QrScanViewModel  _exposureCompensationStep .com.yancao.qrscanner.viewModel.QrScanViewModel  _maxExposureCompensation .com.yancao.qrscanner.viewModel.QrScanViewModel  _minExposureCompensation .com.yancao.qrscanner.viewModel.QrScanViewModel  apply .com.yancao.qrscanner.viewModel.QrScanViewModel  
cameraManager .com.yancao.qrscanner.viewModel.QrScanViewModel  codeIdentify .com.yancao.qrscanner.viewModel.QrScanViewModel  codeIdentifyStatus .com.yancao.qrscanner.viewModel.QrScanViewModel  exposureCompensation .com.yancao.qrscanner.viewModel.QrScanViewModel  filter .com.yancao.qrscanner.viewModel.QrScanViewModel  getAllScanResults .com.yancao.qrscanner.viewModel.QrScanViewModel  getApplication .com.yancao.qrscanner.viewModel.QrScanViewModel  
hasFlashlight .com.yancao.qrscanner.viewModel.QrScanViewModel  initializeCameraControlStates .com.yancao.qrscanner.viewModel.QrScanViewModel  initializeCodeIdentify .com.yancao.qrscanner.viewModel.QrScanViewModel  isFlashlightEnabled .com.yancao.qrscanner.viewModel.QrScanViewModel  
isNotEmpty .com.yancao.qrscanner.viewModel.QrScanViewModel  isScanningEnabled .com.yancao.qrscanner.viewModel.QrScanViewModel  
lastBroadcast .com.yancao.qrscanner.viewModel.QrScanViewModel  lastBroadcastTime .com.yancao.qrscanner.viewModel.QrScanViewModel  launch .com.yancao.qrscanner.viewModel.QrScanViewModel  maxExposureCompensation .com.yancao.qrscanner.viewModel.QrScanViewModel  maxZoomRatio .com.yancao.qrscanner.viewModel.QrScanViewModel  minExposureCompensation .com.yancao.qrscanner.viewModel.QrScanViewModel  minZoomRatio .com.yancao.qrscanner.viewModel.QrScanViewModel  mutableSetOf .com.yancao.qrscanner.viewModel.QrScanViewModel  qrCodePositions .com.yancao.qrscanner.viewModel.QrScanViewModel  realTimeBroadcast .com.yancao.qrscanner.viewModel.QrScanViewModel  realtimeScanResults .com.yancao.qrscanner.viewModel.QrScanViewModel  
sendBroadcast .com.yancao.qrscanner.viewModel.QrScanViewModel  setExposureCompensation .com.yancao.qrscanner.viewModel.QrScanViewModel  setZoomRatio .com.yancao.qrscanner.viewModel.QrScanViewModel  startCamera .com.yancao.qrscanner.viewModel.QrScanViewModel  startRealtimeScanning .com.yancao.qrscanner.viewModel.QrScanViewModel  stopRealtimeScanning .com.yancao.qrscanner.viewModel.QrScanViewModel  toggleFlashlight .com.yancao.qrscanner.viewModel.QrScanViewModel  viewModelScope .com.yancao.qrscanner.viewModel.QrScanViewModel  withContext .com.yancao.qrscanner.viewModel.QrScanViewModel  	zoomRatio .com.yancao.qrscanner.viewModel.QrScanViewModel  Build io.ktor.client  	ByteArray io.ktor.client  CIO io.ktor.client  ContentNegotiation io.ktor.client  	Exception io.ktor.client  
HttpClient io.ktor.client  HttpClientConfig io.ktor.client  HttpResponse io.ktor.client  HttpStatusCode io.ktor.client  Int io.ktor.client  Json io.ktor.client  
JsonObject io.ktor.client  
MessageDigest io.ktor.client  RequiresApi io.ktor.client  String io.ktor.client  UUID io.ktor.client  Unit io.ktor.client  
bodyAsText io.ktor.client  decodeFromString io.ktor.client  format io.ktor.client  get io.ktor.client  	headersOf io.ktor.client  invoke io.ktor.client  java io.ktor.client  joinToString io.ktor.client  
jsonObject io.ktor.client  listOf io.ktor.client  mapOf io.ktor.client  post io.ktor.client  reversed io.ktor.client  setBody io.ktor.client  to io.ktor.client  toByteArray io.ktor.client  toRegex io.ktor.client  toString io.ktor.client  close io.ktor.client.HttpClient  get io.ktor.client.HttpClient  post io.ktor.client.HttpClient  ContentNegotiation io.ktor.client.HttpClientConfig  
expectSuccess io.ktor.client.HttpClientConfig  install io.ktor.client.HttpClientConfig  json io.ktor.client.HttpClientConfig  Build io.ktor.client.engine.cio  	ByteArray io.ktor.client.engine.cio  CIO io.ktor.client.engine.cio  CIOEngineConfig io.ktor.client.engine.cio  ContentNegotiation io.ktor.client.engine.cio  	Exception io.ktor.client.engine.cio  
HttpClient io.ktor.client.engine.cio  HttpResponse io.ktor.client.engine.cio  HttpStatusCode io.ktor.client.engine.cio  Int io.ktor.client.engine.cio  Json io.ktor.client.engine.cio  
JsonObject io.ktor.client.engine.cio  
MessageDigest io.ktor.client.engine.cio  RequiresApi io.ktor.client.engine.cio  String io.ktor.client.engine.cio  UUID io.ktor.client.engine.cio  Unit io.ktor.client.engine.cio  
bodyAsText io.ktor.client.engine.cio  decodeFromString io.ktor.client.engine.cio  format io.ktor.client.engine.cio  get io.ktor.client.engine.cio  	headersOf io.ktor.client.engine.cio  invoke io.ktor.client.engine.cio  java io.ktor.client.engine.cio  joinToString io.ktor.client.engine.cio  
jsonObject io.ktor.client.engine.cio  listOf io.ktor.client.engine.cio  mapOf io.ktor.client.engine.cio  post io.ktor.client.engine.cio  reversed io.ktor.client.engine.cio  setBody io.ktor.client.engine.cio  to io.ktor.client.engine.cio  toByteArray io.ktor.client.engine.cio  toRegex io.ktor.client.engine.cio  toString io.ktor.client.engine.cio  ContentNegotiation )io.ktor.client.plugins.contentnegotiation  Config <io.ktor.client.plugins.contentnegotiation.ContentNegotiation  Plugin <io.ktor.client.plugins.contentnegotiation.ContentNegotiation  json Cio.ktor.client.plugins.contentnegotiation.ContentNegotiation.Config  Build io.ktor.client.request  	ByteArray io.ktor.client.request  CIO io.ktor.client.request  ContentNegotiation io.ktor.client.request  	Exception io.ktor.client.request  
HttpClient io.ktor.client.request  HttpRequestBuilder io.ktor.client.request  HttpResponse io.ktor.client.request  HttpStatusCode io.ktor.client.request  Int io.ktor.client.request  Json io.ktor.client.request  
JsonObject io.ktor.client.request  
MessageDigest io.ktor.client.request  RequiresApi io.ktor.client.request  String io.ktor.client.request  UUID io.ktor.client.request  Unit io.ktor.client.request  
bodyAsText io.ktor.client.request  decodeFromString io.ktor.client.request  format io.ktor.client.request  get io.ktor.client.request  headers io.ktor.client.request  	headersOf io.ktor.client.request  invoke io.ktor.client.request  java io.ktor.client.request  joinToString io.ktor.client.request  
jsonObject io.ktor.client.request  listOf io.ktor.client.request  mapOf io.ktor.client.request  post io.ktor.client.request  reversed io.ktor.client.request  setBody io.ktor.client.request  to io.ktor.client.request  toByteArray io.ktor.client.request  toRegex io.ktor.client.request  toString io.ktor.client.request  headers )io.ktor.client.request.HttpRequestBuilder  invoke )io.ktor.client.request.HttpRequestBuilder  setBody )io.ktor.client.request.HttpRequestBuilder  Build io.ktor.client.statement  	ByteArray io.ktor.client.statement  CIO io.ktor.client.statement  ContentNegotiation io.ktor.client.statement  	Exception io.ktor.client.statement  
HttpClient io.ktor.client.statement  HttpResponse io.ktor.client.statement  HttpStatusCode io.ktor.client.statement  Int io.ktor.client.statement  Json io.ktor.client.statement  
JsonObject io.ktor.client.statement  
MessageDigest io.ktor.client.statement  RequiresApi io.ktor.client.statement  String io.ktor.client.statement  UUID io.ktor.client.statement  Unit io.ktor.client.statement  
bodyAsText io.ktor.client.statement  decodeFromString io.ktor.client.statement  format io.ktor.client.statement  get io.ktor.client.statement  	headersOf io.ktor.client.statement  invoke io.ktor.client.statement  java io.ktor.client.statement  joinToString io.ktor.client.statement  
jsonObject io.ktor.client.statement  listOf io.ktor.client.statement  mapOf io.ktor.client.statement  post io.ktor.client.statement  reversed io.ktor.client.statement  setBody io.ktor.client.statement  to io.ktor.client.statement  toByteArray io.ktor.client.statement  toRegex io.ktor.client.statement  toString io.ktor.client.statement  
bodyAsText %io.ktor.client.statement.HttpResponse  headers %io.ktor.client.statement.HttpResponse  status %io.ktor.client.statement.HttpResponse  Build io.ktor.http  	ByteArray io.ktor.http  CIO io.ktor.http  ContentNegotiation io.ktor.http  	Exception io.ktor.http  Headers io.ktor.http  HeadersBuilder io.ktor.http  
HttpClient io.ktor.http  HttpResponse io.ktor.http  HttpStatusCode io.ktor.http  Int io.ktor.http  Json io.ktor.http  
JsonObject io.ktor.http  
MessageDigest io.ktor.http  RequiresApi io.ktor.http  String io.ktor.http  UUID io.ktor.http  Unit io.ktor.http  Url io.ktor.http  
bodyAsText io.ktor.http  decodeFromString io.ktor.http  format io.ktor.http  get io.ktor.http  	headersOf io.ktor.http  invoke io.ktor.http  java io.ktor.http  joinToString io.ktor.http  
jsonObject io.ktor.http  listOf io.ktor.http  mapOf io.ktor.http  post io.ktor.http  reversed io.ktor.http  setBody io.ktor.http  to io.ktor.http  toByteArray io.ktor.http  toRegex io.ktor.http  toString io.ktor.http  get io.ktor.http.Headers  invoke io.ktor.http.Headers  	appendAll io.ktor.http.HeadersBuilder  invoke io.ktor.http.HeadersBuilder  headers io.ktor.http.HttpMessage  	Companion io.ktor.http.HttpStatusCode  OK io.ktor.http.HttpStatusCode  value io.ktor.http.HttpStatusCode  OK %io.ktor.http.HttpStatusCode.Companion  json "io.ktor.serialization.kotlinx.json  get io.ktor.util.StringValues  	appendAll $io.ktor.util.StringValuesBuilderImpl  Class 	java.lang  	Exception 	java.lang  Runnable 	java.lang  message java.lang.Exception  printStackTrace java.lang.Exception  <SAM-CONSTRUCTOR> java.lang.Runnable  currentTimeMillis java.lang.System  
MessageDigest 
java.security  digest java.security.MessageDigest  getInstance java.security.MessageDigest  Build 	java.util  	ByteArray 	java.util  CIO 	java.util  
Comparator 	java.util  ContentNegotiation 	java.util  	Exception 	java.util  
HttpClient 	java.util  HttpResponse 	java.util  HttpStatusCode 	java.util  Int 	java.util  Json 	java.util  
JsonObject 	java.util  
MessageDigest 	java.util  RequiresApi 	java.util  String 	java.util  UUID 	java.util  Unit 	java.util  
bodyAsText 	java.util  decodeFromString 	java.util  format 	java.util  get 	java.util  	headersOf 	java.util  invoke 	java.util  java 	java.util  joinToString 	java.util  
jsonObject 	java.util  listOf 	java.util  mapOf 	java.util  post 	java.util  reversed 	java.util  setBody 	java.util  to 	java.util  toByteArray 	java.util  toRegex 	java.util  toString 	java.util  
getEncoder java.util.Base64  encode java.util.Base64.Encoder  
randomUUID java.util.UUID  toString java.util.UUID  Executor java.util.concurrent  	Executors java.util.concurrent  shutdown $java.util.concurrent.ExecutorService  newSingleThreadExecutor java.util.concurrent.Executors  Array kotlin  	ByteArray kotlin  CharSequence kotlin  	Function0 kotlin  	Function1 kotlin  	Function2 kotlin  	Function3 kotlin  Lazy kotlin  Nothing kotlin  Pair kotlin  Result kotlin  String kotlin  Suppress kotlin  also kotlin  apply kotlin  getValue kotlin  let kotlin  map kotlin  to kotlin  toList kotlin  toString kotlin  toString 
kotlin.Any  get kotlin.Array  map kotlin.Array  not kotlin.Boolean  format kotlin.ByteArray  joinToString kotlin.ByteArray  String kotlin.Enum  coerceIn kotlin.Float  	compareTo kotlin.Float  div kotlin.Float  minus kotlin.Float  plus kotlin.Float  times kotlin.Float  toInt kotlin.Float  invoke kotlin.Function0  invoke kotlin.Function1  invoke kotlin.Function3  
coerceAtLeast 
kotlin.Int  coerceIn 
kotlin.Int  	compareTo 
kotlin.Int  div 
kotlin.Int  inc 
kotlin.Int  let 
kotlin.Int  minus 
kotlin.Int  plus 
kotlin.Int  rem 
kotlin.Int  times 
kotlin.Int  toFloat 
kotlin.Int  toLong 
kotlin.Int  toString 
kotlin.Int  getValue kotlin.Lazy  provideDelegate kotlin.Lazy  
coerceAtLeast kotlin.Long  	compareTo kotlin.Long  div kotlin.Long  minus kotlin.Long  
component1 kotlin.Pair  
component2 kotlin.Pair  	Companion 
kotlin.String  format 
kotlin.String  invoke 
kotlin.String  
isNotBlank 
kotlin.String  length 
kotlin.String  plus 
kotlin.String  reversed 
kotlin.String  to 
kotlin.String  toByteArray 
kotlin.String  toIntOrNull 
kotlin.String  toRegex 
kotlin.String  format kotlin.String.Companion  invoke kotlin.String.Companion  message kotlin.Throwable  printStackTrace kotlin.Throwable  List kotlin.collections  Map kotlin.collections  MutableList kotlin.collections  
MutableSet kotlin.collections  	emptyList kotlin.collections  filter kotlin.collections  forEach kotlin.collections  getValue kotlin.collections  ifEmpty kotlin.collections  
isNotEmpty kotlin.collections  joinToString kotlin.collections  
lastOrNull kotlin.collections  listOf kotlin.collections  map kotlin.collections  
mapNotNull kotlin.collections  mapOf kotlin.collections  min kotlin.collections  minOf kotlin.collections  
mutableListOf kotlin.collections  mutableSetOf kotlin.collections  reversed kotlin.collections  sortedBy kotlin.collections  take kotlin.collections  takeLast kotlin.collections  toByteArray kotlin.collections  toList kotlin.collections  toString kotlin.collections  toTypedArray kotlin.collections  filter kotlin.collections.List  get kotlin.collections.List  ifEmpty kotlin.collections.List  isEmpty kotlin.collections.List  
isNotEmpty kotlin.collections.List  joinToString kotlin.collections.List  map kotlin.collections.List  size kotlin.collections.List  take kotlin.collections.List  takeLast kotlin.collections.List  add kotlin.collections.MutableList  clear kotlin.collections.MutableList  contains kotlin.collections.MutableList  
isNotEmpty kotlin.collections.MutableList  
lastOrNull kotlin.collections.MutableList  size kotlin.collections.MutableList  toList kotlin.collections.MutableList  toTypedArray kotlin.collections.MutableList  add kotlin.collections.MutableSet  clear kotlin.collections.MutableSet  contains kotlin.collections.MutableSet  size kotlin.collections.MutableSet  minOf kotlin.comparisons  reversed kotlin.comparisons  SuspendFunction1 kotlin.coroutines  println 	kotlin.io  JvmOverloads 
kotlin.jvm  Synchronized 
kotlin.jvm  Volatile 
kotlin.jvm  java 
kotlin.jvm  abs kotlin.math  log kotlin.math  min kotlin.math  CharProgression 
kotlin.ranges  IntProgression 
kotlin.ranges  LongProgression 
kotlin.ranges  UIntProgression 
kotlin.ranges  ULongProgression 
kotlin.ranges  
coerceAtLeast 
kotlin.ranges  coerceIn 
kotlin.ranges  
lastOrNull 
kotlin.ranges  reversed 
kotlin.ranges  
KProperty1 kotlin.reflect  Sequence kotlin.sequences  filter kotlin.sequences  forEach kotlin.sequences  ifEmpty kotlin.sequences  joinToString kotlin.sequences  
lastOrNull kotlin.sequences  map kotlin.sequences  
mapNotNull kotlin.sequences  min kotlin.sequences  minOf kotlin.sequences  sortedBy kotlin.sequences  take kotlin.sequences  toList kotlin.sequences  MatchResult kotlin.text  Regex kotlin.text  String kotlin.text  filter kotlin.text  forEach kotlin.text  format kotlin.text  ifEmpty kotlin.text  
isNotBlank kotlin.text  
isNotEmpty kotlin.text  
lastOrNull kotlin.text  map kotlin.text  
mapNotNull kotlin.text  min kotlin.text  minOf kotlin.text  reversed kotlin.text  take kotlin.text  takeLast kotlin.text  toByteArray kotlin.text  toIntOrNull kotlin.text  toList kotlin.text  toRegex kotlin.text  toString kotlin.text  groupValues kotlin.text.MatchResult  find kotlin.text.Regex  CoroutineDispatcher kotlinx.coroutines  CoroutineScope kotlinx.coroutines  Dispatchers kotlinx.coroutines  Job kotlinx.coroutines  MainCoroutineDispatcher kotlinx.coroutines  launch kotlinx.coroutines  withContext kotlinx.coroutines  Dispatchers !kotlinx.coroutines.CoroutineScope  _codeIdentifyStatus !kotlinx.coroutines.CoroutineScope  codeIdentify !kotlinx.coroutines.CoroutineScope  launch !kotlinx.coroutines.CoroutineScope  
sendBroadcast !kotlinx.coroutines.CoroutineScope  withContext !kotlinx.coroutines.CoroutineScope  IO kotlinx.coroutines.Dispatchers  Main kotlinx.coroutines.Dispatchers  Build kotlinx.serialization.json  	ByteArray kotlinx.serialization.json  CIO kotlinx.serialization.json  ContentNegotiation kotlinx.serialization.json  	Exception kotlinx.serialization.json  
HttpClient kotlinx.serialization.json  HttpResponse kotlinx.serialization.json  HttpStatusCode kotlinx.serialization.json  Int kotlinx.serialization.json  Json kotlinx.serialization.json  JsonElement kotlinx.serialization.json  
JsonObject kotlinx.serialization.json  
MessageDigest kotlinx.serialization.json  RequiresApi kotlinx.serialization.json  String kotlinx.serialization.json  UUID kotlinx.serialization.json  Unit kotlinx.serialization.json  
bodyAsText kotlinx.serialization.json  decodeFromString kotlinx.serialization.json  format kotlinx.serialization.json  get kotlinx.serialization.json  	headersOf kotlinx.serialization.json  invoke kotlinx.serialization.json  java kotlinx.serialization.json  joinToString kotlinx.serialization.json  
jsonObject kotlinx.serialization.json  listOf kotlinx.serialization.json  mapOf kotlinx.serialization.json  post kotlinx.serialization.json  reversed kotlinx.serialization.json  setBody kotlinx.serialization.json  to kotlinx.serialization.json  toByteArray kotlinx.serialization.json  toRegex kotlinx.serialization.json  toString kotlinx.serialization.json  Default kotlinx.serialization.json.Json  decodeFromString kotlinx.serialization.json.Json  decodeFromString 'kotlinx.serialization.json.Json.Default  
jsonObject &kotlinx.serialization.json.JsonElement  toString &kotlinx.serialization.json.JsonElement  get %kotlinx.serialization.json.JsonObject  emptyMap android.app.Activity  emptyMap android.content.Context  emptyMap android.content.ContextWrapper  Map android.graphics  Triple android.graphics  emptyMap android.graphics  get android.graphics  YELLOW android.graphics.Color  emptyMap  android.view.ContextThemeWrapper  Triple android.view.View  emptyMap android.view.View  get android.view.View  emptyMap #androidx.activity.ComponentActivity  emptyMap -androidx.activity.ComponentActivity.Companion  emptyMap (androidx.appcompat.app.AppCompatActivity  emptyMap #androidx.core.app.ComponentActivity  emptyMap &androidx.fragment.app.FragmentActivity  Map com.yancao.qrscanner.ui  Triple com.yancao.qrscanner.ui  emptyMap com.yancao.qrscanner.ui  get com.yancao.qrscanner.ui  Map )com.yancao.qrscanner.ui.QrCodeOverlayView  Triple )com.yancao.qrscanner.ui.QrCodeOverlayView  emptyMap )com.yancao.qrscanner.ui.QrCodeOverlayView  get )com.yancao.qrscanner.ui.QrCodeOverlayView  warningQRPaint )com.yancao.qrscanner.ui.QrCodeOverlayView  warningTextPaint )com.yancao.qrscanner.ui.QrCodeOverlayView  emptyMap &com.yancao.qrscanner.ui.QrScanActivity  Map com.yancao.qrscanner.viewModel  let com.yancao.qrscanner.viewModel  mutableMapOf com.yancao.qrscanner.viewModel  qrCodePositions com.yancao.qrscanner.viewModel  qrCodeVerificationResults com.yancao.qrscanner.viewModel  set com.yancao.qrscanner.viewModel  let .com.yancao.qrscanner.viewModel.QrScanViewModel  mutableMapOf .com.yancao.qrscanner.viewModel.QrScanViewModel  qrCodeVerificationResults .com.yancao.qrscanner.viewModel.QrScanViewModel  set .com.yancao.qrscanner.viewModel.QrScanViewModel  Triple kotlin  let kotlin.Pair  let 
kotlin.String  
component1 
kotlin.Triple  
component2 
kotlin.Triple  
component3 
kotlin.Triple  
MutableMap kotlin.collections  emptyMap kotlin.collections  get kotlin.collections  mutableMapOf kotlin.collections  set kotlin.collections  get kotlin.collections.Map  set kotlin.collections.MutableMap  
MatchGroup kotlin.text  get kotlin.text  set kotlin.text  Pair !kotlinx.coroutines.CoroutineScope  let !kotlinx.coroutines.CoroutineScope  mutableMapOf !kotlinx.coroutines.CoroutineScope  qrCodePositions !kotlinx.coroutines.CoroutineScope  qrCodeVerificationResults !kotlinx.coroutines.CoroutineScope  set !kotlinx.coroutines.CoroutineScope  	emptyList android.app.Activity  	emptyList android.content.Context  	emptyList android.content.ContextWrapper  forEachIndexed android.graphics  
isNotEmpty android.graphics  	emptyList  android.view.ContextThemeWrapper  forEachIndexed android.view.View  
isNotEmpty android.view.View  	emptyList #androidx.activity.ComponentActivity  	emptyList -androidx.activity.ComponentActivity.Companion  	emptyList (androidx.appcompat.app.AppCompatActivity  	emptyList #androidx.core.app.ComponentActivity  	emptyList &androidx.fragment.app.FragmentActivity  	emptyList com.yancao.qrscanner.ui  forEachIndexed com.yancao.qrscanner.ui  forEachIndexed )com.yancao.qrscanner.ui.QrCodeOverlayView  
isNotEmpty )com.yancao.qrscanner.ui.QrCodeOverlayView  copy 8com.yancao.qrscanner.ui.QrCodeOverlayView.DetectedQRInfo  verificationStatus 8com.yancao.qrscanner.ui.QrCodeOverlayView.DetectedQRInfo  	emptyList &com.yancao.qrscanner.ui.QrScanActivity  forEachIndexed kotlin.collections  
isNotEmpty kotlin.collections.Map  forEachIndexed kotlin.collections.MutableList  set kotlin.collections.MutableList  forEachIndexed kotlin.sequences  forEachIndexed kotlin.text  Log com.yancao.qrscanner.viewModel  Log .com.yancao.qrscanner.viewModel.QrScanViewModel  Log !kotlinx.coroutines.CoroutineScope  RED android.graphics.Color  associateQRPaint )com.yancao.qrscanner.ui.QrCodeOverlayView  associateTextPaint )com.yancao.qrscanner.ui.QrCodeOverlayView  
isNotEmpty 
kotlin.String  toString kotlin.collections.MutableSet  
plusAssign com.yancao.qrscanner.viewModel  println com.yancao.qrscanner.viewModel  i .com.yancao.qrscanner.viewModel.QrScanViewModel  
plusAssign .com.yancao.qrscanner.viewModel.QrScanViewModel  println .com.yancao.qrscanner.viewModel.QrScanViewModel  
plusAssign 
kotlin.Int  
plusAssign kotlin.collections  MediatorLiveData androidx.lifecycle  Triple #androidx.lifecycle.MediatorLiveData  	addSource #androidx.lifecycle.MediatorLiveData  apply #androidx.lifecycle.MediatorLiveData  emptyMap #androidx.lifecycle.MediatorLiveData  let #androidx.lifecycle.MediatorLiveData  observe #androidx.lifecycle.MediatorLiveData  qrCodePositions #androidx.lifecycle.MediatorLiveData  qrCodeVerificationResults #androidx.lifecycle.MediatorLiveData  value #androidx.lifecycle.MediatorLiveData  MediatorLiveData com.yancao.qrscanner.viewModel  Triple com.yancao.qrscanner.viewModel  emptyMap com.yancao.qrscanner.viewModel  MediatorLiveData .com.yancao.qrscanner.viewModel.QrScanViewModel  Triple .com.yancao.qrscanner.viewModel.QrScanViewModel  combinedQRData .com.yancao.qrscanner.viewModel.QrScanViewModel  emptyMap .com.yancao.qrscanner.viewModel.QrScanViewModel  first kotlin.Pair  second kotlin.Pair  Long com.yancao.qrscanner.viewModel  isEmpty com.yancao.qrscanner.viewModel  	removeAll com.yancao.qrscanner.viewModel  sortedBy com.yancao.qrscanner.viewModel  take com.yancao.qrscanner.viewModel  toMap com.yancao.qrscanner.viewModel  verifiedQrCodes com.yancao.qrscanner.viewModel  isEmpty .com.yancao.qrscanner.viewModel.QrScanViewModel  qrCodeLastBroadcastTime .com.yancao.qrscanner.viewModel.QrScanViewModel  	removeAll .com.yancao.qrscanner.viewModel.QrScanViewModel  sortedBy .com.yancao.qrscanner.viewModel.QrScanViewModel  take .com.yancao.qrscanner.viewModel.QrScanViewModel  toMap .com.yancao.qrscanner.viewModel.QrScanViewModel  verifiedQrCodes .com.yancao.qrscanner.viewModel.QrScanViewModel  isEmpty kotlin.CharSequence  isEmpty 
kotlin.String  isEmpty kotlin.collections  	removeAll kotlin.collections  toMap kotlin.collections  MutableEntry kotlin.collections.MutableMap  containsKey kotlin.collections.MutableMap  entries kotlin.collections.MutableMap  get kotlin.collections.MutableMap  remove kotlin.collections.MutableMap  size kotlin.collections.MutableMap  toMap kotlin.collections.MutableMap  key *kotlin.collections.MutableMap.MutableEntry  value *kotlin.collections.MutableMap.MutableEntry  	removeAll kotlin.collections.MutableSet  sortedBy kotlin.collections.MutableSet  isEmpty kotlin.text  toMap !kotlinx.coroutines.CoroutineScope  verifiedQrCodes !kotlinx.coroutines.CoroutineScope                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             