package com.yancao.qrscanner.viewModel

import android.app.Application
import android.content.Intent
import android.os.Build
import android.util.Log
import androidx.annotation.OptIn
import androidx.annotation.RequiresApi
import androidx.camera.core.ExperimentalGetImage
import androidx.camera.view.PreviewView
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.google.mlkit.vision.barcode.common.Barcode
import com.yancao.qrscanner.camera.CameraManager
import com.yancao.qrscanner.domain.ScanResultsHolder
import com.yancao.qrscanner.utils.BroadCastUtils
import com.yancao.qrscanner.utils.CodeIdentify
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 * 二维码扫描的 ViewModel
 * 负责管理摄像头操作、扫描状态和结果数据
 *
 * 功能说明：
 * - 管理摄像头的启动和停止
 * - 控制实时扫描的开启和关闭
 * - 处理拍照功能
 * - 管理扫描结果的存储和获取
 * - 提供二维码位置信息用于绘制绿框
 * - 新增：集成闪光灯和缩放控制功能
 */
@ExperimentalGetImage
class QrScanViewModel(application: Application) : AndroidViewModel(application) {

    private val cameraManager = CameraManager(application)

    // 实时扫描结果的 LiveData
    val realtimeScanResults = MutableLiveData<List<String>>()

    // 扫描状态的 LiveData
    val isScanningEnabled = MutableLiveData(false)

    // Pair的第一个元素是检测到的二维码列表，第二个元素是图像尺寸 (width, height)
    val qrCodePositions = MutableLiveData<Pair<List<Barcode>, Pair<Int, Int>>>()

    // 相机控制相关的 LiveData
    val isFlashlightEnabled = MutableLiveData(false)
    val zoomRatio = MutableLiveData(1.0f)
    val minZoomRatio = MutableLiveData(1.0f)
    val maxZoomRatio = MutableLiveData(1.0f)
    val hasFlashlight = MutableLiveData(false)

    // 曝光补偿相关状态
    private val _exposureCompensation = MutableLiveData<Int>()
    val exposureCompensation: LiveData<Int> = _exposureCompensation

    private val _minExposureCompensation = MutableLiveData<Int>()
    val minExposureCompensation: LiveData<Int> = _minExposureCompensation

    private val _maxExposureCompensation = MutableLiveData<Int>()
    val maxExposureCompensation: LiveData<Int> = _maxExposureCompensation

    private val _exposureCompensationStep = MutableLiveData<Float>()

    // 修改现有的防抖延迟为2秒
    private val DEBOUNCE_DELAY = 2000L // 2秒内不重复广播相同内容

    // 记录每个二维码的最后广播时间
    private val qrCodeLastBroadcastTime = mutableMapOf<String, Long>()

    // 记录已核验过的二维码及其结果
    private val verifiedQrCodes = mutableMapOf<String, Int>()

    //二维码核验相关
    // 添加CodeIdentify相关属性
    private var codeIdentify: CodeIdentify? = null
    private val _codeIdentifyStatus = MutableLiveData<String>()
    val codeIdentifyStatus: LiveData<String> = _codeIdentifyStatus
    val qrCodeVerificationResults = MutableLiveData<Map<String, Int>>()


    /**
     * 启动相机
     * @param previewView 预览组件
     * @param lifecycleOwner 生命周期拥有者
     * @param enableRealtimeScanning 是否启用实时扫描，默认为 false
     */
    @ExperimentalGetImage
    fun startCamera(
        previewView: PreviewView,
        lifecycleOwner: LifecycleOwner,
        enableRealtimeScanning: Boolean = false
    ) {
        cameraManager.startCamera(
            previewView = previewView,
            lifecycleOwner = lifecycleOwner,
            enableRealtimeScanning = enableRealtimeScanning,
            onQRCodeDetected = { detectedCodes ->
                // 实时扫描回调，更新 LiveData
                realtimeScanResults.postValue(detectedCodes)
            },
            onQRCodeWithPosition = { barcodes, imageWidth, imageHeight ->
                // 位置信息回调，用于绘制绿框
                qrCodePositions.postValue(Pair(barcodes, Pair(imageWidth, imageHeight)))
            }
        )

        // 新增：初始化相机控制相关状态
        initializeCameraControlStates()
    }

    /**
     * 初始化相机控制状态
     */
    private fun initializeCameraControlStates() {
        // 检查闪光灯支持
        hasFlashlight.postValue(cameraManager.hasFlashlight())

        // 初始化缩放范围
        minZoomRatio.postValue(cameraManager.getMinZoomRatio())
        maxZoomRatio.postValue(cameraManager.getMaxZoomRatio())
        zoomRatio.postValue(cameraManager.getCurrentZoomRatio())

        // 初始化曝光补偿范围
        _minExposureCompensation.postValue(cameraManager.getMinExposureCompensation())
        _maxExposureCompensation.postValue(cameraManager.getMaxExposureCompensation())
        _exposureCompensation.postValue(cameraManager.getCurrentExposureCompensation())
        _exposureCompensationStep.postValue(cameraManager.getExposureCompensationStep())
    }

    /**
     * 开始实时扫描
     * 这个方法会启用摄像头的实时二维码扫描功能
     * @param previewView 预览组件
     * @param lifecycleOwner 生命周期拥有者
     */
    @ExperimentalGetImage
    fun startRealtimeScanning(previewView: PreviewView, lifecycleOwner: LifecycleOwner) {
        isScanningEnabled.postValue(true)

        // 重新启动摄像头，这次启用实时扫描
        cameraManager.startCamera(
            previewView = previewView,
            lifecycleOwner = lifecycleOwner,
            enableRealtimeScanning = true,
            onQRCodeDetected = { detectedCodes ->
                // 更新扫描结果
                realtimeScanResults.postValue(detectedCodes)
                realTimeBroadcast(detectedCodes)
            },
            onQRCodeWithPosition = { barcodes, imageWidth, imageHeight ->
                // 立即更新位置信息，先显示绿框
                qrCodePositions.postValue(Pair(barcodes, Pair(imageWidth, imageHeight)))
            }
        )

        // 重新初始化相机控制状态
        initializeCameraControlStates()
    }

    /**
     * 停止实时扫描
     * @param previewView 预览组件
     * @param lifecycleOwner 生命周期拥有者
     */
    @ExperimentalGetImage
    fun stopRealtimeScanning(previewView: PreviewView, lifecycleOwner: LifecycleOwner) {
        isScanningEnabled.postValue(false)

        // 重新启动摄像头，这次禁用实时扫描
        cameraManager.startCamera(
            previewView = previewView,
            lifecycleOwner = lifecycleOwner,
            enableRealtimeScanning = false,
            onQRCodeDetected = null,
            onQRCodeWithPosition = null
        )

        // 重新初始化相机控制状态
        initializeCameraControlStates()
    }

    /**
     * 切换闪光灯状态
     * @return 操作是否成功
     */
    fun toggleFlashlight(): Boolean {
        val currentState = isFlashlightEnabled.value ?: false
        val newState = !currentState

        return if (cameraManager.setFlashlight(newState)) {
            isFlashlightEnabled.postValue(newState)
            true
        } else {
            false
        }
    }

    /**
     * 实时广播（带代码验证）
     * @param data 检测到的二维码结果组
     */
    private fun realTimeBroadcast(data: List<String>) {
        val currentTime = System.currentTimeMillis()

        data.forEach { qrCode ->
            if (qrCode.isEmpty()) return@forEach

            // 检查是否在2秒内重复广播同一个二维码
            val lastTime = qrCodeLastBroadcastTime[qrCode] ?: 0L
            if (currentTime - lastTime < DEBOUNCE_DELAY) {
                return@forEach // 2秒内不重复广播
            }

            // 检查该二维码是否已经核验过且结果为1（不再广播）
            val verificationResult = verifiedQrCodes[qrCode]
            if (verificationResult == 1) {
                return@forEach // 核验值为1的二维码不再广播
            }

            // 更新最后广播时间
            qrCodeLastBroadcastTime[qrCode] = currentTime

            // 特殊长度的二维码直接发送，不需要网络核验
            if (qrCode.length == 105) {
                sendBroadcast(qrCode)
                return@forEach
            }

            // 先广播，再进行核验
            sendBroadcast(qrCode)

            // 如果该二维码还未核验过，则开启协程进行核验
            if (!verifiedQrCodes.containsKey(qrCode)) {
                viewModelScope.launch(Dispatchers.IO) {
                    try {
                        val result = codeIdentify?.codeClassfy(qrCode) ?: 0

                        // 记录核验结果
                        verifiedQrCodes[qrCode] = result

                        // 更新UI状态
                        val statusMessage = when (result) {
                            0 -> "检测到正常条码: $qrCode"
                            1 -> "检测到未关联的盒条码: $qrCode (后续不再广播)"
                            2 -> "检测到已关联的条码: $qrCode"
                            else -> "代码验证异常: $qrCode"
                        }
                        _codeIdentifyStatus.postValue(statusMessage)

                        // 更新核验结果到LiveData
                        qrCodeVerificationResults.postValue(verifiedQrCodes.toMap())

                    } catch (e: Exception) {
                        _codeIdentifyStatus.postValue("代码验证失败: ${e.message}")
                    }
                }
            }
        }

        // 定期清理过期的时间戳记录（保留最近10分钟的记录）
        val oneHourAgo = currentTime - 600000L
        qrCodeLastBroadcastTime.entries.removeAll { it.value < oneHourAgo }

        // 限制缓存大小，防止内存泄漏
        if (verifiedQrCodes.size > 200) {
            // 清理最老的50个记录
            val sortedEntries = verifiedQrCodes.entries.sortedBy { qrCodeLastBroadcastTime[it.key] ?: 0L }
            sortedEntries.take(50).forEach {
                verifiedQrCodes.remove(it.key)
                qrCodeLastBroadcastTime.remove(it.key)
            }
        }
    }

    /**
     * 发送广播的私有方法
     */
    private fun sendBroadcast(result: String) {
        val broadcastIntent = Intent(BroadCastUtils.BROADCAST_ACTION).apply {
            putExtra(BroadCastUtils.BROADCAST_RAW_DATA, result)
        }
        getApplication<Application>().sendBroadcast(broadcastIntent)
    }


    /**
     * 设置缩放比例
     * @param ratio 缩放比例
     * @return 操作是否成功
     */
    fun setZoomRatio(ratio: Float): Boolean {
        return if (cameraManager.setZoomRatio(ratio)) {
            zoomRatio.postValue(ratio)
            true
        } else {
            false
        }
    }


    /**
     * 获取当前存储的所有扫描结果
     * @return 扫描结果列表
     */
    fun getAllScanResults(): List<String> {
        return ScanResultsHolder.scanResults
    }

    /**
     * 设置曝光补偿
     * @param index 曝光补偿索引
     * @return 操作是否成功
     */
    fun setExposureCompensation(index: Int): Boolean {
        return if (cameraManager.setExposureCompensation(index)) {
            _exposureCompensation.postValue(index)
            true
        } else {
            false
        }
    }

    // 初始化CodeIdentify
    @RequiresApi(Build.VERSION_CODES.O)
    fun initializeCodeIdentify() {
        codeIdentify = CodeIdentify(
            loginUsername = "61541",
            loginPassword = "Jbcj@1#2",
            statusCallback = { status ->
                _codeIdentifyStatus.postValue(status)
            }
        )

        // 在后台线程获取token
        viewModelScope.launch(Dispatchers.IO) {
            try {
                codeIdentify?.tokenGetter()
            } catch (e: Exception) {
                _codeIdentifyStatus.postValue("初始化失败: ${e.message}")
            }
        }
    }

    /**
     * ViewModel 清理时的操作
     * 释放摄像头资源，防止内存泄漏
     */
    @OptIn(ExperimentalGetImage::class)
    override fun onCleared() {
        super.onCleared()
        cameraManager.shutdown()
    }
}